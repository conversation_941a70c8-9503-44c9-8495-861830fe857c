import React from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import { 
  Users, 
  Heart, 
  Zap, 
  Globe, 
  ArrowRight,
  CheckCircle,
  Star,
  Award,
  Shield,
  Clock
} from 'lucide-react';

const GetInvolved: React.FC = () => {
  const volunteerRoles = [
    {
      title: "Emergency Responder",
      description: "Join our rapid response team to provide immediate assistance during disasters.",
      requirements: ["First aid certification", "Physical fitness", "Availability for emergency calls"],
      commitment: "On-call basis",
      icon: Shield
    },
    {
      title: "Community Coordinator",
      description: "Help organize local disaster preparedness programs and community outreach.",
      requirements: ["Strong communication skills", "Local community knowledge", "Event planning experience"],
      commitment: "10-15 hours/month",
      icon: Users
    },
    {
      title: "Technical Support",
      description: "Assist with platform development, data analysis, and technical infrastructure.",
      requirements: ["Programming skills", "Problem-solving abilities", "Tech background"],
      commitment: "Flexible hours",
      icon: Zap
    },
    {
      title: "Training Instructor",
      description: "Teach disaster preparedness and response skills to community members.",
      requirements: ["Teaching experience", "Emergency management knowledge", "Public speaking skills"],
      commitment: "5-10 hours/month",
      icon: Award
    }
  ];

  const benefits = [
    "Professional development opportunities",
    "Networking with emergency management professionals",
    "Training and certification programs",
    "Recognition and awards for outstanding service",
    "Flexible scheduling options",
    "Make a real difference in your community"
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="pt-16">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-green-600 via-blue-600 to-purple-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl lg:text-6xl font-black mb-6">
                Get Involved
              </h1>
              <p className="text-xl lg:text-2xl text-green-100 max-w-4xl mx-auto leading-relaxed mb-8">
                Join thousands of volunteers making a difference in disaster response and community resilience. 
                Your skills and passion can save lives.
              </p>
              <Link
                to="/volunteer"
                className="inline-flex items-center bg-white text-blue-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start Volunteering Today
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
            </div>
          </div>
        </section>

        {/* Why Volunteer Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-4xl font-black text-gray-900 mb-6">Why Volunteer With Us?</h2>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Volunteering with DisasterWatch means joining a global community of dedicated individuals 
                  committed to making the world safer and more resilient. Every contribution, no matter how 
                  small, creates ripple effects that can save lives and strengthen communities.
                </p>
                
                <div className="space-y-4">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                      <span className="text-gray-700">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-2xl text-center">
                  <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                  <div className="text-3xl font-black text-gray-900 mb-2">50K+</div>
                  <div className="text-gray-600 text-sm">Active Volunteers</div>
                </div>
                <div className="bg-green-50 p-6 rounded-2xl text-center">
                  <Globe className="w-12 h-12 text-green-600 mx-auto mb-4" />
                  <div className="text-3xl font-black text-gray-900 mb-2">120+</div>
                  <div className="text-gray-600 text-sm">Countries Served</div>
                </div>
                <div className="bg-purple-50 p-6 rounded-2xl text-center">
                  <Heart className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                  <div className="text-3xl font-black text-gray-900 mb-2">1M+</div>
                  <div className="text-gray-600 text-sm">Lives Impacted</div>
                </div>
                <div className="bg-orange-50 p-6 rounded-2xl text-center">
                  <Clock className="w-12 h-12 text-orange-600 mx-auto mb-4" />
                  <div className="text-3xl font-black text-gray-900 mb-2">24/7</div>
                  <div className="text-gray-600 text-sm">Response Ready</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Volunteer Roles Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Volunteer Opportunities</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Find the perfect role that matches your skills, interests, and availability.
              </p>
            </div>
            
            <div className="grid lg:grid-cols-2 gap-8">
              {volunteerRoles.map((role, index) => (
                <div key={index} className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                  <div className="flex items-start space-x-4 mb-6">
                    <div className="bg-blue-100 p-3 rounded-xl">
                      <role.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{role.title}</h3>
                      <p className="text-gray-600 leading-relaxed">{role.description}</p>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Requirements:</h4>
                    <ul className="space-y-2">
                      {role.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="flex items-center space-x-2">
                          <Star className="w-4 h-4 text-yellow-500" />
                          <span className="text-gray-700 text-sm">{req}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-sm text-gray-500">Time Commitment:</span>
                      <div className="font-medium text-gray-900">{role.commitment}</div>
                    </div>
                    <Link
                      to="/volunteer"
                      className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm"
                    >
                      Apply Now
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
          <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center">
            <h2 className="text-4xl lg:text-5xl font-black mb-6">
              Ready to Make a Difference?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Join our community of heroes who are working to make the world safer, one community at a time.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/volunteer"
                className="bg-white text-blue-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center"
              >
                <Users className="mr-2 w-5 h-5" />
                Become a Volunteer
              </Link>
              <Link
                to="/contact"
                className="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-4 rounded-2xl font-bold hover:bg-white/20 transition-all duration-300 flex items-center justify-center"
              >
                <Heart className="mr-2 w-5 h-5" />
                Learn More
              </Link>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default GetInvolved;
