import React from 'react';
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import { 
  AlertTriangle, 
  Shield, 
  Users, 
  Globe, 
  Zap, 
  Eye, 
  Heart, 
  Clock,
  MapPin,
  Phone,
  CheckCircle
} from 'lucide-react';

const WhatWeDo: React.FC = () => {
  const services = [
    {
      icon: AlertTriangle,
      title: "Disaster Reporting",
      description: "Real-time incident reporting system that allows communities to quickly alert authorities and neighbors about emergencies.",
      features: ["Instant notifications", "GPS location tracking", "Photo/video evidence", "Multi-language support"]
    },
    {
      icon: Eye,
      title: "Response Coordination",
      description: "Advanced coordination tools that help emergency responders, volunteers, and organizations work together effectively.",
      features: ["Resource allocation", "Team communication", "Task management", "Progress tracking"]
    },
    {
      icon: Shield,
      title: "Early Warning System",
      description: "Proactive monitoring and alert system that warns communities about potential disasters before they strike.",
      features: ["Weather monitoring", "Seismic detection", "Flood prediction", "Automated alerts"]
    },
    {
      icon: Users,
      title: "Community Network",
      description: "Platform that connects neighbors, volunteers, and local organizations to build stronger, more resilient communities.",
      features: ["Volunteer matching", "Skill sharing", "Resource pooling", "Community forums"]
    }
  ];

  const impact = [
    { number: "500K+", label: "Lives Protected", icon: Shield },
    { number: "1,200+", label: "Communities Served", icon: Globe },
    { number: "50K+", label: "Volunteers Connected", icon: Users },
    { number: "24/7", label: "Emergency Response", icon: Clock }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="pt-16">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl lg:text-6xl font-black mb-6">
                What We Do
              </h1>
              <p className="text-xl lg:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
                We provide comprehensive disaster management solutions that save lives, 
                protect communities, and build resilience against future emergencies.
              </p>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Our Core Services</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Comprehensive tools and services designed to handle every aspect of disaster management.
              </p>
            </div>
            
            <div className="grid lg:grid-cols-2 gap-12">
              {services.map((service, index) => (
                <div key={index} className="bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
                  <div className="flex items-start space-x-4">
                    <div className="bg-blue-100 p-3 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">{service.title}</h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>
                      <ul className="space-y-2">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-gray-700 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">How It Works</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our streamlined process ensures rapid response and effective coordination during emergencies.
              </p>
            </div>
            
            <div className="grid md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">1. Report</h3>
                <p className="text-gray-600 text-sm">Community members report incidents through our platform</p>
              </div>
              
              <div className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Eye className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">2. Verify</h3>
                <p className="text-gray-600 text-sm">Our team verifies and categorizes the emergency</p>
              </div>
              
              <div className="text-center">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">3. Coordinate</h3>
                <p className="text-gray-600 text-sm">Resources and responders are coordinated for action</p>
              </div>
              
              <div className="text-center">
                <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">4. Support</h3>
                <p className="text-gray-600 text-sm">Ongoing support and recovery assistance</p>
              </div>
            </div>
          </div>
        </section>

        {/* Impact Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Our Impact</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Real numbers that demonstrate the difference we're making in communities worldwide.
              </p>
            </div>
            
            <div className="grid md:grid-cols-4 gap-8">
              {impact.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <stat.icon className="w-10 h-10 text-blue-600" />
                  </div>
                  <div className="text-4xl font-black text-gray-900 mb-2">{stat.number}</div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default WhatWeDo;
