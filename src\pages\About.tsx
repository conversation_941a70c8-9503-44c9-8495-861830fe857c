import React from 'react';
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import { Shield, Users, Globe, Award, Heart, Target } from 'lucide-react';

const About: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="pt-16">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl lg:text-6xl font-black mb-6">
                About DisasterWatch
              </h1>
              <p className="text-xl lg:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
                We're building the world's most advanced disaster management platform to connect communities, 
                coordinate responses, and save lives through intelligent technology.
              </p>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-4xl font-black text-gray-900 mb-6">Our Mission</h2>
                <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                  DisasterWatch was founded with a simple yet powerful mission: to revolutionize how communities 
                  prepare for, respond to, and recover from disasters. We believe that technology can bridge the 
                  gap between those who need help and those who can provide it.
                </p>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Our platform empowers individuals, organizations, and governments to work together seamlessly 
                  during critical moments when every second counts.
                </p>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-2xl text-center">
                  <Shield className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Safety First</h3>
                  <p className="text-gray-600 text-sm">Prioritizing community safety in every decision</p>
                </div>
                <div className="bg-green-50 p-6 rounded-2xl text-center">
                  <Users className="w-12 h-12 text-green-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Community</h3>
                  <p className="text-gray-600 text-sm">Connecting people when they need it most</p>
                </div>
                <div className="bg-purple-50 p-6 rounded-2xl text-center">
                  <Globe className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Global Impact</h3>
                  <p className="text-gray-600 text-sm">Making a difference worldwide</p>
                </div>
                <div className="bg-orange-50 p-6 rounded-2xl text-center">
                  <Award className="w-12 h-12 text-orange-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Excellence</h3>
                  <p className="text-gray-600 text-sm">Committed to the highest standards</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Our Values</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                These core values guide everything we do and shape how we serve communities worldwide.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
                <Heart className="w-12 h-12 text-red-500 mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Compassion</h3>
                <p className="text-gray-600 leading-relaxed">
                  We approach every situation with empathy and understanding, recognizing that behind every 
                  report is a human being in need of help.
                </p>
              </div>
              
              <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
                <Target className="w-12 h-12 text-blue-500 mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Precision</h3>
                <p className="text-gray-600 leading-relaxed">
                  Accurate information saves lives. We're committed to providing precise, verified data 
                  to enable effective disaster response.
                </p>
              </div>
              
              <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
                <Users className="w-12 h-12 text-green-500 mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Collaboration</h3>
                <p className="text-gray-600 leading-relaxed">
                  Disasters affect entire communities. We believe in the power of collective action and 
                  bringing people together to create positive change.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-black text-gray-900 mb-6">Our Team</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Meet the dedicated professionals working around the clock to make communities safer.
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-lg text-gray-600 mb-8">
                Our diverse team of engineers, disaster response experts, and community advocates 
                brings together decades of experience in emergency management and technology.
              </p>
              <div className="inline-flex items-center space-x-2 text-blue-600 font-medium">
                <Users className="w-5 h-5" />
                <span>50+ Team Members Worldwide</span>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default About;
